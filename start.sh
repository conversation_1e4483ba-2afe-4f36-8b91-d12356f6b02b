#!/bin/bash

# 故事讲述器启动脚本

echo "=== 故事讲述器启动脚本 ==="

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3，请先安装Python3"
    exit 1
fi

# 检查Redis是否运行
if ! command -v redis-cli &> /dev/null; then
    echo "警告: 未找到Redis，请确保Redis已安装并运行"
    echo "在macOS上可以使用: brew install redis && brew services start redis"
    echo "在Ubuntu上可以使用: sudo apt install redis-server && sudo systemctl start redis"
fi

# 创建虚拟环境（如果不存在）
if [ ! -d "venv" ]; then
    echo "创建Python虚拟环境..."
    python3 -m venv venv
fi

# 激活虚拟环境
echo "激活虚拟环境..."
source venv/bin/activate

# 安装依赖
echo "安装Python依赖包..."
pip install -r requirements.txt

# 创建必要的目录
echo "创建必要的目录..."
mkdir -p static/uploads
mkdir -p logs

# 初始化数据库
echo "初始化数据库..."
python run.py init-db 2>/dev/null || echo "数据库已存在"

echo ""
echo "=== 启动说明 ==="
echo "1. 确保Redis服务正在运行"
echo "2. 在一个终端中启动Celery工作进程:"
echo "   celery -A celery_worker.celery worker --loglevel=info"
echo ""
echo "3. 在另一个终端中启动Flask应用:"
echo "   python run.py"
echo ""
echo "4. 打开浏览器访问: http://localhost:5000"
echo "   默认用户名: admin"
echo "   默认密码: admin123"
echo ""
echo "=== 开发模式快速启动 ==="
echo "如果要快速启动开发环境，请运行:"
echo "./dev_start.sh"
echo ""
