import os
from pathlib import Path
from sqlalchemy.pool import NullPool

basedir = Path(__file__).parent.absolute()

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or f'sqlite:///{basedir / "app.db"}'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    # 禁用SQLAlchemy连接池以支持Celery多进程并发
    SQLALCHEMY_ENGINE_OPTIONS = {
        'poolclass': NullPool
    }
    
    # 文件上传配置
    UPLOAD_FOLDER = basedir / 'static' / 'uploads'
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file size
    ALLOWED_EXTENSIONS = {'txt'}
    
    # Celery配置
    CELERY_BROKER_URL = os.environ.get('CELERY_BROKER_URL') or 'redis://localhost:6379/0'
    CELERY_RESULT_BACKEND = os.environ.get('CELERY_RESULT_BACKEND') or 'redis://localhost:6379/0'
    
    # 故事优化配置
    API_KEYS = [os.environ.get('OPENAI_API_KEY') or 'sk-Rqz9v0slHocJSz2GZi325SePr0U1ZjuYYtvxzabKXi282A0S']
    API_BASE_URL = os.environ.get('API_BASE_URL') or 'https://oapi.xmly.dev/v1'
    
    # 确保上传目录存在
    def __init__(self):
        self.UPLOAD_FOLDER.mkdir(parents=True, exist_ok=True)

class DevelopmentConfig(Config):
    DEBUG = True

class ProductionConfig(Config):
    DEBUG = False

config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
