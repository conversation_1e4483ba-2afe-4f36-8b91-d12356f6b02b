/* 自定义样式 - 简约高级配色方案 */

:root {
    --primary-color: #2c3e50;
    --primary-hover: #34495e;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --light-bg: #f8f9fa;
    --border-color: #e9ecef;
    --text-color: #2c3e50;
    --text-muted: #6c757d;
    --shadow-light: 0 2px 8px rgba(44, 62, 80, 0.08);
    --shadow-medium: 0 4px 16px rgba(44, 62, 80, 0.12);
}

.drag-area {
    border: 2px dashed var(--border-color);
    border-radius: 12px;
    padding: 40px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    background-color: var(--light-bg);
    color: var(--text-muted);
}

.drag-area:hover {
    border-color: var(--primary-color);
    background-color: #ffffff;
    color: var(--text-color);
}

.drag-area.dragover {
    border-color: var(--primary-color);
    background-color: #ffffff;
    transform: scale(1.01);
    box-shadow: var(--shadow-light);
}

.task-card {
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
    border-radius: 12px;
    background-color: #ffffff;
}

.task-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-medium);
    border-color: var(--primary-color);
}

.progress-container {
    position: relative;
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 0.8rem;
    font-weight: 600;
    color: #fff;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

.status-badge {
    font-size: 0.75rem;
    padding: 0.3rem 0.6rem;
    border-radius: 6px;
    font-weight: 500;
}

.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
    color: #ffffff !important;
}

.card-header {
    background-color: var(--light-bg);
    border-bottom: 1px solid var(--border-color);
    border-radius: 12px 12px 0 0 !important;
}

.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: #ffffff;
}

.btn-primary:hover {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-light);
}

.alert {
    border-radius: 10px;
    border: none;
}

.form-control, .form-select {
    border-radius: 8px;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
    color: var(--text-color);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(44, 62, 80, 0.15);
}

.table th {
    border-top: none;
    font-weight: 600;
    color: var(--text-color);
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* 状态颜色统一 */
.bg-success, .badge.bg-success {
    background-color: var(--success-color) !important;
}

.bg-warning, .badge.bg-warning {
    background-color: var(--warning-color) !important;
}

.bg-danger, .badge.bg-danger {
    background-color: var(--danger-color) !important;
}

.bg-primary, .badge.bg-primary {
    background-color: var(--primary-color) !important;
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-success:hover {
    background-color: #229954;
    border-color: #229954;
    transform: translateY(-1px);
    box-shadow: var(--shadow-light);
}

.btn-warning {
    background-color: var(--warning-color);
    border-color: var(--warning-color);
}

.btn-warning:hover {
    background-color: #e67e22;
    border-color: #e67e22;
    transform: translateY(-1px);
    box-shadow: var(--shadow-light);
}

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

.btn-danger:hover {
    background-color: #c0392b;
    border-color: #c0392b;
    transform: translateY(-1px);
    box-shadow: var(--shadow-light);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: var(--shadow-light);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .task-card {
        margin-bottom: 1rem;
    }

    .drag-area {
        padding: 20px;
    }

    .btn-group-vertical .btn {
        margin-bottom: 0.5rem;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* 加载动画 */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 日志容器样式 - 简化配色 */
.log-container {
    background-color: #2c3e50;
    color: #ecf0f1;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    font-size: 0.85rem;
    line-height: 1.5;
    padding: 20px;
    border-radius: 10px;
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
}

.log-container::-webkit-scrollbar {
    width: 6px;
}

.log-container::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.log-container::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.log-container::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* 额外的高级样式优化 */
.container {
    max-width: 1200px;
}

/* 卡片阴影层次 */
.card {
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: var(--shadow-medium);
}

/* 输入框组样式 */
.input-group .form-control {
    border-right: none;
}

.input-group .btn {
    border-left: none;
}

/* 列表组样式 */
.list-group-item {
    border-color: var(--border-color);
    color: var(--text-color);
}

.list-group-item:hover {
    background-color: var(--light-bg);
}

/* 模态框样式 */
.modal-content {
    border: none;
    border-radius: 12px;
    box-shadow: var(--shadow-medium);
}

.modal-header {
    border-bottom: 1px solid var(--border-color);
    background-color: var(--light-bg);
    border-radius: 12px 12px 0 0;
}

.modal-footer {
    border-top: 1px solid var(--border-color);
    background-color: var(--light-bg);
    border-radius: 0 0 12px 12px;
}

/* 分页样式 */
.page-link {
    color: var(--primary-color);
    border-color: var(--border-color);
}

.page-link:hover {
    color: var(--primary-hover);
    background-color: var(--light-bg);
    border-color: var(--primary-color);
}

.page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* 工具提示样式 */
.tooltip-inner {
    background-color: var(--primary-color);
    color: #ffffff;
}

/* 下拉菜单样式 */
.dropdown-menu {
    border: none;
    border-radius: 8px;
    box-shadow: var(--shadow-medium);
    border: 1px solid var(--border-color);
}

.dropdown-item:hover {
    background-color: var(--light-bg);
    color: var(--text-color);
}

/* 面包屑样式 */
.breadcrumb {
    background-color: transparent;
    padding: 0;
}

.breadcrumb-item a {
    color: var(--primary-color);
    text-decoration: none;
}

.breadcrumb-item a:hover {
    color: var(--primary-hover);
}

/* 徽章样式统一 */
.badge {
    font-weight: 500;
    letter-spacing: 0.025em;
}

/* 文本选择样式 */
::selection {
    background-color: rgba(44, 62, 80, 0.2);
    color: var(--text-color);
}
