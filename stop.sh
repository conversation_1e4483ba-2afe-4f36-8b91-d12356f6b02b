#!/bin/bash

# 停止所有服务的脚本

echo "=== 停止故事讲述器服务 ==="

# 停止Celery工作进程
echo "停止Celery工作进程..."
pkill -f "celery.*worker" 2>/dev/null || echo "Celery工作进程未运行"

# 停止Flask应用
echo "停止Flask应用..."
pkill -f "python.*run.py" 2>/dev/null || echo "Flask应用未运行"

# 可选：停止Redis（如果是通过brew启动的）
read -p "是否停止Redis服务? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    if command -v brew &> /dev/null; then
        brew services stop redis
    elif command -v systemctl &> /dev/null; then
        sudo systemctl stop redis
    else
        echo "请手动停止Redis服务"
    fi
fi

echo "所有服务已停止"
