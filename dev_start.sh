#!/bin/bash

# 开发环境快速启动脚本

echo "=== 故事讲述器 - 开发环境启动 ==="

# 检查Redis是否运行
redis_running=$(redis-cli ping 2>/dev/null)
if [ "$redis_running" != "PONG" ]; then
    echo "启动Redis服务..."
    if command -v brew &> /dev/null; then
        # macOS with Homebrew
        brew services start redis
    elif command -v systemctl &> /dev/null; then
        # Linux with systemd
        sudo systemctl start redis
    else
        echo "请手动启动Redis服务"
        exit 1
    fi
    sleep 2
fi

# 激活虚拟环境
if [ -d "venv" ]; then
    source venv/bin/activate
else
    echo "虚拟环境不存在，请先运行 ./start.sh"
    exit 1
fi

# 创建日志目录
mkdir -p logs

# 启动Celery工作进程（后台运行）
echo "启动Celery工作进程..."
celery -A celery_worker.celery worker --loglevel=info --logfile=logs/celery.log --detach

# 等待Celery启动
sleep 3

# 启动Flask应用
echo "启动Flask应用..."
echo "访问地址: http://localhost:5000"
echo "默认用户名: admin"
echo "默认密码: admin123"
echo ""
echo "按 Ctrl+C 停止服务"
echo ""

# 设置环境变量
export FLASK_ENV=development
export FLASK_DEBUG=1

# 启动Flask应用
python run.py
