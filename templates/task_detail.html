{% extends "base.html" %}

{% block title %}任务详情 - {{ task.task_name }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="bi bi-file-text"></i> {{ task.task_name }}</h2>
    <div>
        {% if task.is_completed and task.output_path %}
        <a href="{{ url_for('main.download_result', task_id=task.id) }}" class="btn btn-success">
            <i class="bi bi-download"></i> 下载结果
        </a>
        {% endif %}
        <a href="{{ url_for('main.dashboard') }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> 返回控制台
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <!-- 任务状态卡片 -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="bi bi-info-circle"></i> 任务状态</h5>
                {% if task.status == 'completed' %}
                    <span class="badge bg-success">已完成</span>
                {% elif task.status == 'processing' %}
                    <span class="badge bg-warning">处理中</span>
                {% elif task.status == 'failed' %}
                    <span class="badge bg-danger">失败</span>
                {% else %}
                    <span class="badge bg-secondary">等待中</span>
                {% endif %}
            </div>
            <div class="card-body">
                {% if task.status == 'processing' %}
                <div class="progress-container mb-3">
                    <div class="progress" style="height: 25px;">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" 
                             role="progressbar" 
                             style="width: {{ task.progress }}%"
                             id="taskProgress">
                        </div>
                    </div>
                    <div class="progress-text" id="progressText">{{ task.progress }}%</div>
                </div>
                <div id="statusMessage" class="text-muted mb-3">正在处理中...</div>
                {% endif %}

                {% if task.error_message %}
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle"></i> 
                    <strong>错误信息：</strong>{{ task.error_message }}
                </div>
                {% endif %}

                <div class="row">
                    <div class="col-md-6">
                        <p><strong>创建时间：</strong>{{ task.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</p>
                        {% if task.started_at %}
                        <p><strong>开始时间：</strong>{{ task.started_at.strftime('%Y-%m-%d %H:%M:%S') }}</p>
                        {% endif %}
                        {% if task.completed_at %}
                        <p><strong>完成时间：</strong>{{ task.completed_at.strftime('%Y-%m-%d %H:%M:%S') }}</p>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        {% if task.total_chapters > 0 %}
                        <p><strong>总章节数：</strong>{{ task.total_chapters }}</p>
                        <p><strong>已处理章节：</strong><span id="processedChapters">{{ task.processed_chapters }}</span></p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- 实时日志 -->
        {% if task.status == 'processing' %}
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-terminal"></i> 处理日志</h5>
            </div>
            <div class="card-body">
                <div id="logContainer" style="height: 300px; overflow-y: auto; background-color: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 0.9rem;">
                    <div id="logContent">等待日志信息...</div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <div class="col-md-4">
        <!-- 任务配置信息 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-gear"></i> 配置信息</h5>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>原始文件：</strong></td>
                        <td>{{ task.original_filename }}</td>
                    </tr>
                    <tr>
                        <td><strong>主角名称：</strong></td>
                        <td>{{ task.character }}</td>
                    </tr>
                    <tr>
                        <td><strong>书名：</strong></td>
                        <td>{{ task.book_name }}</td>
                    </tr>
                    <tr>
                        <td><strong>频道：</strong></td>
                        <td>{{ task.channel }}</td>
                    </tr>
                    <tr>
                        <td><strong>人称：</strong></td>
                        <td>第{{ task.person }}人称</td>
                    </tr>
                    {% if task.start_chapter is defined and task.start_chapter %}
                    <tr>
                        <td><strong>起始章节：</strong></td>
                        <td>第{{ task.start_chapter }}章</td>
                    </tr>
                    {% endif %}
                    {% if task.end_chapter is defined and task.end_chapter %}
                    <tr>
                        <td><strong>结束章节：</strong></td>
                        <td>第{{ task.end_chapter }}章</td>
                    </tr>
                    {% endif %}
                    {% if task.chapters_per_batch is defined and task.chapters_per_batch %}
                    <tr>
                        <td><strong>批次大小：</strong></td>
                        <td>{{ task.chapters_per_batch }}章/批</td>
                    </tr>
                    {% endif %}
                </table>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-tools"></i> 操作</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    {% if task.is_completed and task.output_path %}
                    <a href="{{ url_for('main.download_result', task_id=task.id) }}" class="btn btn-success">
                        <i class="bi bi-download"></i> 下载优化结果
                    </a>
                    {% endif %}
                    
                    {% if task.status == 'processing' %}
                    <button class="btn btn-warning" onclick="refreshStatus()">
                        <i class="bi bi-arrow-clockwise"></i> 刷新状态
                    </button>
                    <button class="btn btn-info" onclick="debugStatus()">
                        <i class="bi bi-bug"></i> 调试信息
                    </button>
                    {% endif %}
                    
                    <form method="POST" action="{{ url_for('main.delete_task', task_id=task.id) }}"
                          onsubmit="return confirmDeleteTask({{ task.id }})">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                        <button type="submit" class="btn btn-danger w-100">
                            <i class="bi bi-trash"></i> 删除任务
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let logLines = [];
let isNavigating = false; // 防止重复导航

// 删除确认函数
function confirmDeleteTask(taskId) {
    console.log('删除任务请求:', taskId);
    addLogLine(`准备删除任务: ${taskId}`);
    const confirmed = confirm('确定要删除这个任务吗？这将删除所有相关文件。');
    if (confirmed) {
        console.log('用户确认删除任务:', taskId);
        addLogLine(`用户确认删除任务: ${taskId}`);
        addLogLine('停止状态监控...');

        // 立即停止轮询
        stopStatusMonitoring();

        // 显示删除中的消息
        addLogLine('正在删除任务，请稍候...');
    } else {
        console.log('用户取消删除任务:', taskId);
        addLogLine(`用户取消删除任务: ${taskId}`);
    }
    return confirmed;
}

function updateTaskStatus() {
    // 如果正在导航，不执行更新
    if (isNavigating) {
        return;
    }

    fetch(`/api/task/{{ task.id }}/status`)
        .then(response => {
            if (!response.ok) {
                // 如果是404错误，说明任务已被删除
                if (response.status === 404) {
                    if (!isNavigating) {
                        isNavigating = true;
                        addLogLine('任务已被删除，返回控制台...');
                        stopStatusMonitoring();
                        setTimeout(() => {
                            window.location.href = '{{ url_for("main.dashboard") }}';
                        }, 2000);
                    }
                    return null;
                }
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            // 如果data为null（任务被删除），直接返回
            if (data === null) {
                return;
            }

            // 检查是否有错误
            if (data.error) {
                addLogLine(`API错误: ${data.error}`);
                // 如果是任务不存在的错误，返回控制台
                if (data.error.includes('not found') || data.error.includes('不存在')) {
                    if (!isNavigating) {
                        isNavigating = true;
                        addLogLine('任务不存在，返回控制台...');
                        stopStatusMonitoring();
                        setTimeout(() => {
                            window.location.href = '{{ url_for("main.dashboard") }}';
                        }, 2000);
                    }
                }
                return;
            }

            // 更新进度条
            const progressBar = document.getElementById('taskProgress');
            const progressText = document.getElementById('progressText');
            const statusMessage = document.getElementById('statusMessage');
            const processedChapters = document.getElementById('processedChapters');

            if (progressBar && progressText && data.progress !== undefined) {
                progressBar.style.width = `${data.progress}%`;
                progressText.textContent = `${data.progress}%`;
            }

            // 更新状态消息
            let currentStatus = data.status || '正在处理中...';
            if (statusMessage) {
                statusMessage.textContent = currentStatus;
            }

            // 更新章节进度
            if (processedChapters && data.chapters_current !== undefined) {
                processedChapters.textContent = data.chapters_current;

                if (progressText && data.chapters_total) {
                    progressText.textContent = `${data.progress}% (${data.chapters_current}/${data.chapters_total})`;
                }
            }

            // 更新日志 - 只有当状态真正改变时才添加日志
            const lastLogMessage = logLines.length > 0 ? logLines[logLines.length - 1] : '';
            const newLogMessage = currentStatus;

            if (newLogMessage && !lastLogMessage.includes(newLogMessage)) {
                addLogLine(newLogMessage);
            }

            // 如果有错误消息，显示它
            if (data.error_message) {
                addLogLine(`错误: ${data.error_message}`);
            }

            // 如果任务完成或失败，刷新页面
            if (data.status === 'completed' || data.status === 'failed') {
                if (!isNavigating) {
                    isNavigating = true;
                    addLogLine(data.status === 'completed' ? '任务已完成！' : '任务失败');
                    stopStatusMonitoring();
                    setTimeout(() => location.reload(), 2000);
                }
            }
        })
        .catch(error => {
            console.error('Error updating status:', error);
            addLogLine(`网络错误: ${error.message}`);

            // 如果是404错误，可能是任务被删除了
            if (error.message.includes('404') && !isNavigating) {
                isNavigating = true;
                addLogLine('任务可能已被删除，停止监控并返回控制台...');
                stopStatusMonitoring();
                setTimeout(() => {
                    window.location.href = '{{ url_for("main.dashboard") }}';
                }, 3000);
            }
        });
}

function addLogLine(message) {
    const timestamp = new Date().toLocaleTimeString();
    const logLine = `[${timestamp}] ${message}`;
    
    logLines.push(logLine);
    
    const logContent = document.getElementById('logContent');
    if (logContent) {
        logContent.innerHTML = logLines.join('<br>');
        
        // 滚动到底部
        const logContainer = document.getElementById('logContainer');
        if (logContainer) {
            logContainer.scrollTop = logContainer.scrollHeight;
        }
    }
}

function refreshStatus() {
    updateTaskStatus();
}

function debugStatus() {
    fetch(`/api/debug/task/{{ task.id }}`)
        .then(response => response.json())
        .then(data => {
            addLogLine('=== 调试信息 ===');
            addLogLine(`任务状态: ${data.status}`);
            addLogLine(`进度: ${data.progress}%`);
            addLogLine(`Celery任务ID: ${data.celery_task_id || '无'}`);
            if (data.celery_state) {
                addLogLine(`Celery状态: ${data.celery_state}`);
            }
            if (data.celery_error) {
                addLogLine(`Celery错误: ${data.celery_error}`);
            }
            if (data.error_message) {
                addLogLine(`错误信息: ${data.error_message}`);
            }
            addLogLine('=== 调试信息结束 ===');
        })
        .catch(error => {
            addLogLine(`调试信息获取失败: ${error.message}`);
        });
}

// 启动状态监控
function startStatusMonitoring() {
    // 如果已经有监控在运行，先停止
    if (window.statusInterval) {
        clearInterval(window.statusInterval);
        window.statusInterval = null;
    }

    {% if task.status == 'processing' %}
    if (!isNavigating) {
        addLogLine('开始监控任务状态...');
        addLogLine('任务ID: {{ task.id }}');
        addLogLine('当前状态: {{ task.status }}');

        // 每3秒更新一次状态
        window.statusInterval = setInterval(updateTaskStatus, 3000);

        // 立即更新一次
        updateTaskStatus();
    }
    {% else %}
    addLogLine('任务状态: {{ task.status }}');
    addLogLine('任务不在处理中，不启动状态监控');
    {% endif %}
}

// 停止状态监控
function stopStatusMonitoring() {
    if (window.statusInterval) {
        clearInterval(window.statusInterval);
        window.statusInterval = null;
        addLogLine('已停止状态监控');
    }
}

// 页面可见性变化处理
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        // 页面隐藏时暂停监控
        if (window.statusInterval) {
            addLogLine('页面隐藏，暂停状态监控');
            clearInterval(window.statusInterval);
            window.statusInterval = null;
        }
    } else {
        // 页面显示时恢复监控（仅当任务仍在处理中且没有在导航）
        if (!window.statusInterval && '{{ task.status }}' === 'processing' && !isNavigating) {
            addLogLine('页面显示，恢复状态监控');
            window.statusInterval = setInterval(updateTaskStatus, 3000);
            updateTaskStatus();
        }
    }
});

// 页面加载时启动监控
document.addEventListener('DOMContentLoaded', startStatusMonitoring);

// 页面卸载时停止监控
window.addEventListener('beforeunload', stopStatusMonitoring);
</script>
{% endblock %}
