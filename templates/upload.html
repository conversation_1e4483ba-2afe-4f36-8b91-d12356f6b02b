{% extends "base.html" %}

{% block title %}上传文件 - 故事讲述器{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card shadow">
            <div class="card-header text-white" style="background-color: var(--primary-color);">
                <h4 class="mb-0">
                    <i class="bi bi-cloud-upload"></i> 上传文件优化
                    <small class="ms-2 opacity-75">上传TXT文件进行故事优化</small>
                </h4>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data" id="uploadForm">
                    {{ form.hidden_tag() }}
                    
                    <!-- 文件上传区域 -->
                    <div class="mb-4">
                        <label class="form-label">选择TXT文件</label>
                        <div class="drag-area" id="dragArea">
                            <i class="bi bi-cloud-upload display-4 text-muted"></i>
                            <p class="mt-2 mb-2">拖拽文件到此处或点击选择</p>
                            {{ form.file(class="d-none", id="fileInput") }}
                            <button type="button" class="btn btn-outline-primary" id="selectFileBtn">
                                选择文件
                            </button>
                        </div>
                        <div id="fileInfo" class="mt-2 text-muted" style="display: none;">
                            <i class="bi bi-file-text"></i> <span id="fileName"></span> 
                            (<span id="fileSize"></span>)
                        </div>
                        {% if form.file.errors %}
                            <div class="text-danger mt-2">
                                {% for error in form.file.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- 优化参数 -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.character.label(class="form-label") }}
                            {{ form.character(class="form-control" + (" is-invalid" if form.character.errors else "")) }}
                            {% if form.character.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.character.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.book_name.label(class="form-label") }}
                            {{ form.book_name(class="form-control" + (" is-invalid" if form.book_name.errors else "")) }}
                            {% if form.book_name.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.book_name.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.channel.label(class="form-label") }}
                            {{ form.channel(class="form-select" + (" is-invalid" if form.channel.errors else "")) }}
                            {% if form.channel.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.channel.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.person.label(class="form-label") }}
                            {{ form.person(class="form-select" + (" is-invalid" if form.person.errors else "")) }}
                            {% if form.person.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.person.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- 提示信息 -->
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i>
                        <strong>提示：</strong>系统会自动识别文件中的章节结构，采用一章一章的方式进行故事优化处理，支持并发处理以提高效率。
                    </div>

                    <div class="alert alert-success">
                        <i class="bi bi-lightning"></i>
                        <strong>优化特色：</strong>
                        <ul class="mb-0 mt-2">
                            <li>智能章节识别和分割</li>
                            <li>逐章优化，保持故事连贯性</li>
                            <li>并发处理，提升处理速度</li>
                            <li>自动维护章节间的接续关系</li>
                        </ul>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('main.dashboard') }}" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i> 返回
                        </a>
                        {{ form.submit(class="btn btn-primary", id="submitBtn") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const dragArea = document.getElementById('dragArea');
    const fileInput = document.getElementById('fileInput');
    const selectFileBtn = document.getElementById('selectFileBtn');
    const fileInfo = document.getElementById('fileInfo');
    const fileName = document.getElementById('fileName');
    const fileSize = document.getElementById('fileSize');
    const submitBtn = document.getElementById('submitBtn');
    const uploadForm = document.getElementById('uploadForm');

    // 拖拽功能
    dragArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        dragArea.classList.add('dragover');
    });

    dragArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        dragArea.classList.remove('dragover');
    });

    dragArea.addEventListener('drop', function(e) {
        e.preventDefault();
        dragArea.classList.remove('dragover');

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            fileInput.files = files;
            updateFileInfo(files[0]);
        }
    });

    // 点击拖拽区域选择文件（但排除按钮点击）
    dragArea.addEventListener('click', function(e) {
        // 如果点击的是按钮，不触发文件选择
        if (e.target === selectFileBtn || selectFileBtn.contains(e.target)) {
            return;
        }
        fileInput.click();
    });

    // 按钮点击事件
    selectFileBtn.addEventListener('click', function(e) {
        e.stopPropagation(); // 阻止事件冒泡到拖拽区域
        fileInput.click();
    });

    fileInput.addEventListener('change', function() {
        if (this.files.length > 0) {
            updateFileInfo(this.files[0]);
        }
    });

    function updateFileInfo(file) {
        fileName.textContent = file.name;
        fileSize.textContent = formatFileSize(file.size);
        fileInfo.style.display = 'block';
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // 表单提交时显示加载状态
    uploadForm.addEventListener('submit', function() {
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> 上传中...';
    });
});
</script>
{% endblock %}
