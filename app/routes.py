"""
Simplified Flask routes using service layer architecture
"""
from flask import Blueprint, render_template, redirect, url_for, jsonify, send_file, flash
from flask_login import login_required, current_user
from app import login_manager
from app.models import User
from app.forms import LoginForm, UploadForm, QuickAdaptForm
from app.services.auth_service import AuthService
from app.services.task_service import TaskService
from app.services.form_service import FormService

bp = Blueprint('main', __name__)


@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))


# ============================================================================
# Authentication Routes
# ============================================================================

@bp.route('/')
def index():
    """Home page - redirect based on authentication status"""
    if AuthService.is_authenticated():
        return redirect(url_for('main.dashboard'))
    return redirect(url_for('main.login'))


@bp.route('/login', methods=['GET', 'POST'])
def login():
    """User login page"""
    if AuthService.is_authenticated():
        return redirect(url_for('main.dashboard'))
    
    form = LoginForm()
    if FormService.validate_login_form(form):
        if AuthService.authenticate_user(form.username.data, form.password.data):
            return redirect(AuthService.get_redirect_after_login())
    
    return render_template('login.html', form=form)


@bp.route('/logout')
@login_required
def logout():
    """User logout"""
    AuthService.logout_current_user()
    return redirect(url_for('main.login'))


# ============================================================================
# Dashboard and Task Management Routes
# ============================================================================

@bp.route('/dashboard')
@login_required
def dashboard():
    """User dashboard showing all tasks"""
    tasks = TaskService.get_user_tasks()
    return render_template('dashboard.html', tasks=tasks)


@bp.route('/task/<int:task_id>')
@login_required
def task_detail(task_id):
    """Task detail page"""
    task = TaskService.get_user_task(task_id)
    return render_template('task_detail.html', task=task)


@bp.route('/delete_task/<int:task_id>', methods=['POST'])
@login_required
def delete_task(task_id):
    """Delete a task"""
    import logging
    logger = logging.getLogger(__name__)

    logger.info(f"收到删除任务请求: task_id={task_id}, user_id={current_user.id}")

    success = TaskService.delete_task(task_id)
    if success:
        logger.info(f"任务删除成功: task_id={task_id}")
    else:
        logger.error(f"任务删除失败: task_id={task_id}")

    return redirect(url_for('main.dashboard'))


# ============================================================================
# File Upload and Processing Routes
# ============================================================================

@bp.route('/upload', methods=['GET', 'POST'])
@login_required
def upload():
    """File upload page"""
    form = UploadForm()
    
    if FormService.validate_upload_form(form):
        form_data = FormService.extract_form_data(form)
        task = TaskService.create_task_from_upload(form.file.data, form_data)
        
        if task:
            return redirect(url_for('main.task_detail', task_id=task.id))
    
    return render_template('upload.html', form=form)


@bp.route('/quick', methods=['GET', 'POST'])
@login_required
def quick_adapt():
    """Quick adaptation page"""
    form = QuickAdaptForm()
    
    if FormService.validate_quick_form(form):
        form_data = FormService.extract_form_data(form)
        task = TaskService.create_task_from_content(form.content.data, form_data)
        
        if task:
            return redirect(url_for('main.task_detail', task_id=task.id))
    
    return render_template('quick.html', form=form)


# ============================================================================
# File Download Routes
# ============================================================================

@bp.route('/download/<int:task_id>')
@login_required
def download_result(task_id):
    """Download task result file"""
    task = TaskService.get_user_task(task_id)
    
    if not TaskService.is_task_downloadable(task):
        flash('任务尚未完成或没有输出文件！', 'error')
        return redirect(url_for('main.task_detail', task_id=task_id))
    
    return send_file(
        task.output_path,
        as_attachment=True,
        download_name=f"{task.task_name}_adapted.txt"
    )


# ============================================================================
# API Routes
# ============================================================================

@bp.route('/api/task/<int:task_id>/status')
@login_required
def task_status(task_id):
    """Get task status API"""
    try:
        result = TaskService.get_task_status(task_id)

        if 'error' in result:
            return jsonify(result), 500

        return jsonify(result)
    except Exception as e:
        # 如果任务不存在，返回404
        if 'not found' in str(e).lower() or '404' in str(e):
            return jsonify({'error': '任务不存在'}), 404
        return jsonify({'error': '获取任务状态失败', 'message': str(e)}), 500


@bp.route('/api/debug/task/<int:task_id>')
@login_required
def debug_task_status(task_id):
    """Debug task status API"""
    try:
        debug_info = TaskService.get_debug_info(task_id)

        if 'error' in debug_info:
            return jsonify(debug_info), 500

        return jsonify(debug_info)
    except Exception as e:
        # 如果任务不存在，返回404
        if 'not found' in str(e).lower() or '404' in str(e):
            return jsonify({'error': '任务不存在'}), 404
        return jsonify({'error': '获取调试信息失败', 'message': str(e)}), 500
