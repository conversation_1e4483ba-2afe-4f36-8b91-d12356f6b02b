"""
API客户端封装类
提供统一的API调用接口
"""
import logging
from typing import Dict, List, Optional
from openai import OpenAI
from flask import current_app
from app.utils.text_utils import BlockError, filter_think_tags

logger = logging.getLogger(__name__)


class APIClient:
    """API客户端封装类"""
    
    def __init__(self):
        """初始化API客户端"""
        self.client = None
        self.model_name = "gemini-2.5-pro-preview-06-05"
        self._configure_client()
    
    def _configure_client(self) -> None:
        """配置API客户端"""
        api_key = current_app.config.get('API_KEYS', [''])[0] if current_app.config.get('API_KEYS') else ''
        base_url = current_app.config.get('API_BASE_URL', 'https://oapi.xmly.dev/v1')
        
        self.client = OpenAI(
            api_key=api_key,
            base_url=base_url
        )
        logger.info(f"API客户端已配置，使用模型: {self.model_name}")
    
    def send_chat_completion(self, messages: List[Dict[str, str]], max_retries: int = 1) -> Optional[str]:
        """
        发送聊天完成请求
        
        Args:
            messages: 消息列表
            max_retries: 最大重试次数
            
        Returns:
            处理后的响应内容，失败时返回None
        """
        for retry in range(max_retries):
            try:
                response = self.client.chat.completions.create(
                    model=self.model_name,
                    messages=messages,
                    temperature=1,
                    top_p=0.95,
                    max_tokens=32000,
                    timeout=800,
                )

                if 'No candidates returned' in str(response):
                    raise BlockError("API返回无候选结果")

                # 过滤掉<think></think>标签及其内容
                content = response.choices[0].message.content
                filtered_content = filter_think_tags(content)

                return filtered_content

            except BlockError as e:
                logger.warning(f"API被阻止: {str(e)}")
                raise e
            except Exception as e:
                logger.error(f"API调用错误 (重试 {retry + 1}/{max_retries}): {str(e)}")
                if retry == max_retries - 1:
                    return None
        
        return None
    
    def is_available(self) -> bool:
        """检查API客户端是否可用"""
        return self.client is not None
    
    def get_model_name(self) -> str:
        """获取当前使用的模型名称"""
        return self.model_name


# 全局API客户端实例
_api_client = None


def get_api_client() -> APIClient:
    """获取全局API客户端实例"""
    global _api_client
    if _api_client is None:
        _api_client = APIClient()
    return _api_client


def create_api_client() -> APIClient:
    """创建新的API客户端实例"""
    return APIClient()
