# Prompt Templates

这个目录包含了故事优化系统使用的所有Jinja2模板文件。

## 模板文件说明

### 1. system_prompt.j2
系统提示词模板，定义了AI的角色和基本指导原则。

**变量：**
- `person`: 人称（如"三"表示第三人称）
- `channel`: 频道类型（如"玄幻"、"都市"等）

### 2. initial_prompt.j2
初始重写提示词模板，用于第一次文本重写。

**变量：**
- `pre_text`: 前文内容
- `character`: 主角名称
- `text_length`: 原文长度
- `min_output_length`: 最小输出长度
- `max_output_length`: 最大输出长度
- `text`: 要重写的原文本

### 3. optimize_prompt.j2
优化重写提示词模板，用于对第一次重写结果进行优化。

**变量：**
- `original_text`: 原始文本
- `first_rewrite`: 第一次重写的结果

### 4. further_rewrite_prompt.j2
进一步重写提示词模板，用于处理相似度过高的文本段落。

**变量：**
- `similar_chunks`: 相似文本块列表，每个元素包含：
  - `text`: 文本内容
  - `similarity`: 相似度分数

## 使用方式

在`TextRewriter`类中，模板通过Jinja2环境加载和渲染：

```python
# 初始化模板环境
template_dir = Path(__file__).parent / 'prompt_templates'
self.jinja_env = Environment(loader=FileSystemLoader(str(template_dir)))

# 使用模板
template = self.jinja_env.get_template('system_prompt.j2')
prompt = template.render(person=self.person, channel=self.channel)
```

## 修改模板

如果需要修改prompt内容，直接编辑对应的`.j2`文件即可。修改后的模板会在下次使用时自动生效，无需重启应用。

## 模板语法

使用标准的Jinja2语法：
- `{{ variable }}`: 变量替换
- `{% for item in list %}...{% endfor %}`: 循环
- `{% if condition %}...{% endif %}`: 条件判断
- `{{ "%.2f"|format(number) }}`: 格式化过滤器

更多语法请参考：https://jinja.palletsprojects.com/en/3.1.x/templates/
