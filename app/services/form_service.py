"""
Form handling service for processing form validation and errors
"""
import logging
from typing import Dict, Any
from flask import flash, current_app, request

logger = logging.getLogger(__name__)


class FormService:
    """Service for handling form operations"""
    
    @staticmethod
    def extract_form_data(form) -> Dict[str, Any]:
        """Extract form data into a dictionary"""
        return {
            'character': form.character.data,
            'book_name': form.book_name.data,
            'channel': form.channel.data,
            'person': form.person.data
        }
    
    @staticmethod
    def handle_form_errors(form, operation_name: str = "表单"):
        """Handle form validation errors"""
        if request.method == 'POST':
            current_app.logger.error(f"{operation_name}验证失败: {form.errors}")
            for field, errors in form.errors.items():
                for error in errors:
                    flash(f'{field}: {error}', 'error')
    
    @staticmethod
    def validate_upload_form(form) -> bool:
        """Validate upload form and handle errors"""
        if form.validate_on_submit():
            return True
        
        FormService.handle_form_errors(form, "文件上传表单")
        return False
    
    @staticmethod
    def validate_quick_form(form) -> bool:
        """Validate quick adaptation form and handle errors"""
        if form.validate_on_submit():
            return True
        
        FormService.handle_form_errors(form, "快速优化表单")
        return False
    
    @staticmethod
    def validate_login_form(form) -> bool:
        """Validate login form"""
        return form.validate_on_submit()
