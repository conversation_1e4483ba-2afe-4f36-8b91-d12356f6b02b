"""
Authentication service for handling user login/logout operations
"""
import logging
from flask import flash, redirect, url_for, request
from flask_login import login_user, logout_user, current_user
from app.models import User

logger = logging.getLogger(__name__)


class AuthService:
    """Service for handling authentication operations"""
    
    @staticmethod
    def authenticate_user(username: str, password: str) -> bool:
        """Authenticate user with username and password"""
        user = User.query.filter_by(username=username).first()
        if user and user.check_password(password):
            login_user(user)
            flash('登录成功！', 'success')
            return True
        else:
            flash('用户名或密码错误！', 'error')
            return False
    
    @staticmethod
    def logout_current_user():
        """Logout current user"""
        logout_user()
        flash('已退出登录。', 'info')
    
    @staticmethod
    def get_redirect_after_login():
        """Get redirect URL after successful login"""
        next_page = request.args.get('next')
        return next_page if next_page else url_for('main.dashboard')
    
    @staticmethod
    def is_authenticated() -> bool:
        """Check if current user is authenticated"""
        return current_user.is_authenticated
