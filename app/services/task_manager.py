"""
Task management service for handling adaptation tasks
"""
import logging
import time
from datetime import datetime, timezone
from pathlib import Path
from typing import List, Dict, Any
from celery import group
from app import db
from app.models import AdaptationTask
from app.utils.file_utils import split_text_into_chapters, create_chapter_files
from app.utils.task_utils import merge_adapted_files
from app.utils.chapter_utils import get_chapter_files_sorted, filter_chapter_files, extract_chapter_number

logger = logging.getLogger(__name__)


class TaskManager:
    """Service for managing adaptation tasks"""
    
    def __init__(self, task_id: int):
        """Initialize task manager"""
        self.task_id = task_id
        self.task = AdaptationTask.query.get(task_id)
        if not self.task:
            raise Exception(f"Task {task_id} not found")
    
    def update_task_status(self, status: str, **kwargs):
        """Update task status and other fields"""
        self.task.status = status
        for key, value in kwargs.items():
            if hasattr(self.task, key):
                setattr(self.task, key, value)
        db.session.commit()
    
    def update_progress(self, celery_task, current: int, total: int, status: str):
        """Update task progress"""
        progress = int((current / total) * 100) if total > 0 else 0
        self.task.progress = progress
        db.session.commit()
        
        celery_task.update_state(
            state='PROGRESS',
            meta={
                'current': progress,
                'total': 100,
                'status': status
            }
        )
    
    def prepare_chapters(self, celery_task) -> tuple[Path, List[Path]]:
        """Prepare chapters for processing"""
        # Read original file
        with open(self.task.file_path, 'r', encoding='utf-8') as f:
            text_content = f.read()
        
        # Split chapters
        celery_task.update_state(state='PROGRESS', meta={'current': 10, 'total': 100, 'status': '分析章节结构...'})
        chapters = split_text_into_chapters(text_content)
        
        if not chapters:
            raise Exception("无法识别章节结构")
        
        # Update total chapters
        self.task.total_chapters = len(chapters)
        db.session.commit()
        
        # Create chapter files
        celery_task.update_state(state='PROGRESS', meta={'current': 20, 'total': 100, 'status': '创建章节文件...'})
        chapters_dir, _ = create_chapter_files(chapters, self.task.file_path)
        
        # Set up output directory
        output_dir = Path(self.task.file_path).parent / f"{Path(self.task.file_path).stem}_adapted"
        output_dir.mkdir(parents=True, exist_ok=True)
        
        return Path(chapters_dir), output_dir
    
    def get_chapter_files_to_process(self, chapters_dir: Path) -> List[Path]:
        """Get list of chapter files to process"""
        input_files = get_chapter_files_sorted(chapters_dir, exclude_rewrite=True)
        input_files = filter_chapter_files(
            input_files,
            getattr(self.task, 'start_chapter', None),
            getattr(self.task, 'end_chapter', None)
        )
        
        if not input_files:
            raise Exception("没有找到要处理的章节文件")
        
        return input_files
    
    def create_chapter_tasks(self, input_files: List[Path], output_dir: Path) -> List:
        """Create chapter processing tasks"""
        from app.tasks import process_single_chapter_task
        
        chapter_tasks = []
        for file_path in input_files:
            chapter_num = extract_chapter_number(file_path)
            chapter_task = process_single_chapter_task.s(
                task_id=self.task_id,
                chapter_num=chapter_num,
                chapter_file_path=str(file_path),
                output_dir=str(output_dir),
                character=self.task.character,
                book_name=self.task.book_name,
                channel=self.task.channel,
                person=self.task.person,
                num_attempts=1
            )
            chapter_tasks.append(chapter_task)
        
        return chapter_tasks
    
    def monitor_chapter_processing(self, celery_task, job, total_chapters: int) -> List[Dict[str, Any]]:
        """Monitor chapter processing progress"""
        completed_count = 0
        
        while not job.ready():
            # Check completed tasks count
            current_completed = sum(1 for r in job.results if r.ready())
            if current_completed > completed_count:
                completed_count = current_completed
                progress = 50 + int((completed_count / total_chapters) * 40)  # 50-90% progress
                celery_task.update_state(state='PROGRESS', meta={
                    'current': progress,
                    'total': 100,
                    'status': f'已完成{completed_count}/{total_chapters}个章节'
                })
            
            # Short wait to avoid too frequent checks
            time.sleep(2)
        
        # Get all results
        chapter_results = job.get()
        
        # Check for failed tasks
        failed_chapters = []
        for i, chapter_result in enumerate(chapter_results):
            if not chapter_result or chapter_result.get('status') != 'completed':
                failed_chapters.append(i + 1)
        
        if failed_chapters:
            logger.warning(f"以下章节处理失败: {failed_chapters}")
        
        logger.info(f"章节处理完成，成功: {len(chapter_results) - len(failed_chapters)}, 失败: {len(failed_chapters)}")
        
        return chapter_results
    
    def finalize_task(self, celery_task, output_dir: Path) -> str:
        """Finalize task and merge output files"""
        celery_task.update_state(state='PROGRESS', meta={'current': 95, 'total': 100, 'status': '合并输出文件...'})
        final_output_path = merge_adapted_files(output_dir, self.task)
        
        # Update task completion status
        self.task.status = 'completed'
        self.task.completed_at = datetime.now(timezone.utc)
        self.task.output_path = final_output_path
        self.task.progress = 100
        db.session.commit()
        
        celery_task.update_state(state='SUCCESS', meta={'current': 100, 'total': 100, 'status': '故事优化完成！'})
        
        return final_output_path
    
    def handle_task_failure(self, celery_task, error_msg: str):
        """Handle task failure"""
        logger.error(f"任务处理失败: {error_msg}")
        
        self.task.status = 'failed'
        self.task.error_message = error_msg
        self.task.completed_at = datetime.now(timezone.utc)
        db.session.commit()
        
        celery_task.update_state(state='FAILURE', meta={'error': error_msg, 'status': '任务失败'})
