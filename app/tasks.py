"""
Organized Celery tasks for story adaptation processing
"""
import logging
from datetime import datetime, timezone
from celery import group
from app import celery
from app.services.task_manager import TaskManager
from app.services.chapter_processor import ChapterProcessor

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@celery.task
def process_single_chapter_task(task_id, chapter_num, chapter_file_path, output_dir,
                               character, book_name, channel, person, num_attempts=1):
    """处理单个章节的Celery任务"""
    try:
        processor = ChapterProcessor(
            task_id=task_id,
            character=character,
            book_name=book_name,
            channel=channel,
            person=person
        )
        
        return processor.process_single_chapter(
            chapter_num=chapter_num,
            chapter_file_path=chapter_file_path,
            output_dir=output_dir,
            num_attempts=num_attempts
        )
        
    except Exception as e:
        error_msg = f"处理第{chapter_num}章失败: {str(e)}"
        logger.error(error_msg)
        raise Exception(error_msg)


@celery.task(bind=True)
def process_adaptation_task(self, task_id):
    """处理故事优化任务"""
    try:
        # Initialize task manager
        task_manager = TaskManager(task_id)
        
        # Update task status to processing
        task_manager.update_task_status(
            'processing',
            started_at=datetime.now(timezone.utc),
            celery_task_id=self.request.id
        )
        
        # Update initial progress
        self.update_state(state='PROGRESS', meta={'current': 0, 'total': 100, 'status': '开始处理...'})
        
        # Prepare chapters for processing
        chapters_dir, output_dir = task_manager.prepare_chapters(self)
        
        # Initialize story optimization engine
        self.update_state(state='PROGRESS', meta={'current': 30, 'total': 100, 'status': '初始化故事优化引擎...'})
        
        # Start story optimization processing
        self.update_state(state='PROGRESS', meta={'current': 40, 'total': 100, 'status': '开始故事优化处理...'})
        
        # Get chapter files to process
        input_files = task_manager.get_chapter_files_to_process(chapters_dir)
        
        # Create chapter processing tasks
        chapter_tasks = task_manager.create_chapter_tasks(input_files, output_dir)
        
        # Execute chapter processing concurrently
        self.update_state(state='PROGRESS', meta={
            'current': 50, 
            'total': 100, 
            'status': f'开始并发处理{len(chapter_tasks)}个章节...'
        })
        
        job = group(chapter_tasks)
        result = job.apply_async()

        # Monitor chapter processing progress
        task_manager.monitor_chapter_processing(self, result, len(chapter_tasks))
        
        # Finalize task and merge output files
        final_output_path = task_manager.finalize_task(self, output_dir)
        
        return {
            'status': 'completed',
            'output_path': final_output_path,
            'total_chapters': task_manager.task.total_chapters,
            'processed_chapters': task_manager.task.processed_chapters
        }
        
    except Exception as e:
        error_msg = str(e)
        logger.error(f"任务处理失败: {error_msg}")
        
        # Handle task failure
        if 'task_manager' in locals():
            task_manager.handle_task_failure(self, error_msg)
        else:
            # Fallback error handling if task_manager wasn't created
            self.update_state(state='FAILURE', meta={'error': error_msg, 'status': '任务失败'})
        
        raise Exception(error_msg)
