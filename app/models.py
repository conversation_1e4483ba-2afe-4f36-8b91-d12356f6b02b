from datetime import datetime
from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from app import db

class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    is_active = db.Column(db.<PERSON><PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 关联的故事优化任务
    adaptation_tasks = db.relationship('AdaptationTask', backref='user', lazy='dynamic')
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def __repr__(self):
        return f'<User {self.username}>'

class AdaptationTask(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.<PERSON>Key('user.id'), nullable=False)
    
    # 任务基本信息
    task_name = db.Column(db.String(200), nullable=False)
    original_filename = db.Column(db.String(200), nullable=False)
    file_path = db.Column(db.String(500), nullable=False)
    
    # 故事优化参数
    character = db.Column(db.String(100), nullable=False)
    book_name = db.Column(db.String(200), nullable=False)
    channel = db.Column(db.String(20), nullable=False)  # 男频/女频
    person = db.Column(db.String(10), nullable=False)   # 第一人称/第三人称
    
    # 任务状态
    status = db.Column(db.String(20), default='pending')  # pending, processing, completed, failed
    celery_task_id = db.Column(db.String(100))
    progress = db.Column(db.Integer, default=0)  # 0-100
    
    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    started_at = db.Column(db.DateTime)
    completed_at = db.Column(db.DateTime)
    
    # 结果
    output_path = db.Column(db.String(500))
    error_message = db.Column(db.Text)
    
    # 统计信息
    total_chapters = db.Column(db.Integer, default=0)
    processed_chapters = db.Column(db.Integer, default=0)
    
    def __repr__(self):
        return f'<AdaptationTask {self.task_name}>'
    
    @property
    def is_completed(self):
        return self.status == 'completed'
    
    @property
    def is_failed(self):
        return self.status == 'failed'
    
    @property
    def is_processing(self):
        return self.status == 'processing'
