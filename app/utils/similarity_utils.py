"""
Text similarity analysis utilities
"""
import re
import logging
from typing import List
from difflib import <PERSON>quence<PERSON>atch<PERSON>


def find_similar_chunks(text2: str, text1: str, chunk_size: int = 200, overlap: int = 50,
                       threshold: float = 0.3, min_pairs: int = 5, max_pairs: int = 10) -> List:
    """Find similar chunks between two texts using SequenceMatcher."""
    try:
        # 先移除特定字符，再移除空白字符
        texts = []
        for text in [text2, text1]:
            text = re.sub(r'[「」""''\u3000]', '', text)
            text = re.sub(r'\s', '', text)
            texts.append(text)

        def create_chunks(text: str) -> List[str]:
            sentences = re.split(r'([。！？])', text)
            sentences = [''.join(i) for i in zip(sentences[0::2], sentences[1::2] + [''])]
            sentences = [s for s in sentences if s.strip()]

            chunks = []
            current_chunk = []
            current_length = 0

            for sentence in sentences:
                sentence_length = len(sentence)

                if current_length + sentence_length > chunk_size and current_chunk:
                    chunks.append(''.join(current_chunk))
                    current_chunk = [current_chunk[-1]] if current_chunk else []
                    current_length = len(current_chunk[0]) if current_chunk else 0

                current_chunk.append(sentence)
                current_length += sentence_length

            if current_chunk:
                chunks.append(''.join(current_chunk))

            return chunks

        chunks_pairs = [create_chunks(text) for text in texts]

        if not chunks_pairs[0] or not chunks_pairs[1]:
            return []

        # 存储所有相似度超过阈值的配对
        all_pairs = []

        for chunk1 in chunks_pairs[0]:
            chunk_pairs = []
            for chunk2 in chunks_pairs[1]:
                similarity = SequenceMatcher(None, chunk1, chunk2).ratio()
                if similarity > threshold:
                    chunk_pairs.append((chunk1, chunk2, similarity))

            # 如果当前chunk1有匹配项，选择相似度最高的一个
            if chunk_pairs:
                best_pair = max(chunk_pairs, key=lambda x: x[2])
                all_pairs.append(best_pair)

        # 按相似度降序排序
        all_pairs.sort(key=lambda x: x[2], reverse=True)

        # 如果匹配数量少于min_pairs且有门槛值限制，逐步降低阈值直到满足最小数量
        if len(all_pairs) < min_pairs and threshold > 0.1:
            return find_similar_chunks(
                text2, text1, chunk_size, overlap,
                max(threshold - 0.1, 0.1),  # 不要低于0.1
                min_pairs, max_pairs
            )

        # 返回前max_pairs个结果，但如果结果数量小于min_pairs则返回所有结果
        return all_pairs[:max_pairs] if len(all_pairs) >= min_pairs else all_pairs

    except Exception as e:
        logging.error(f"Error in finding similar chunks: {str(e)}")
        return []
