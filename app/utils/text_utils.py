"""
Text processing, validation, and quality checking utilities
"""
import re
import time
import logging
from typing import Dict, List, Optional, Callable, Any

# 配置日志
logger = logging.getLogger(__name__)


class BlockError(Exception):
    """自定义异常类，用于处理被阻止的请求"""
    pass


def is_allowed_character(char: str) -> bool:
    """检查字符是否为允许的字符类型（数字、英文、中文）"""
    if not char or char.isspace():
        return True

    # 检查数字
    if char.isdigit():
        return True

    # 检查英文字母
    if 'a' <= char.lower() <= 'z':
        return True

    # 检查中文字符
    if '\u4e00' <= char <= '\u9fff':
        return True

    # 标点符号等常见字符
    # DO NOT EDIT!
    common_symbols = set(''',.!?;:'"()「」[]{}<>+-*/=_@#$%&|\\~`''""…—–-，？。！；：""（）【】《》+-*/=""、'' ·＝～％￥$€£¥§©®™¢¤№℃°℉‰‱∞♀♂” ‘ “ ’\t\n\r\f\v''')

    # 添加更多中文标点符号变体
    chinese_punctuation = set('，。！？；：""''（）【】《》〈〉「」『』〔〕［］｛｝〖〗…—–―‥‧·•‰‱°′″‴※§¶†‡◦‣⁃⁎⁏⁐⁑⁒⁓⁔⁕⁖⁗⁘⁙⁚⁛⁜⁝⁞')
    common_symbols.update(chinese_punctuation)
    if char in common_symbols:
        return True

    return False


def calculate_file_non_allowed_ratio(text: str) -> tuple:
    """计算整个文件中非法字符的占比"""
    non_space_chars = [char for char in text if not char.isspace()]
    if not non_space_chars:
        return 0.0, set(), 0, 0

    total_chars = len(non_space_chars)
    non_allowed_chars = set()
    non_allowed_count = 0

    for char in text:
        if not char.isspace() and not is_allowed_character(char):
            non_allowed_chars.add(char)
            non_allowed_count += 1

    ratio = non_allowed_count / total_chars if total_chars > 0 else 0
    return ratio, non_allowed_chars, non_allowed_count, total_chars


def retry_operation(operation: Callable, max_retries: int = 3, delay: float = 1.0, *args, **kwargs) -> Optional[Dict[str, Any]]:
    """通用重试函数"""
    for retry in range(max_retries):
        try:
            result = operation(*args, **kwargs)

            # 检查是否有有效结果
            if result and result.get("rewritten_text"):
                rewritten_text = result.get("rewritten_text")

                # 移除括号内容
                rewritten_text = re.sub(r"（.*?）", "", rewritten_text)
                rewritten_text = re.sub(r"\(.*?\)", "", rewritten_text)

                ratio, non_allowed_chars, non_allowed_count, total_chars = calculate_file_non_allowed_ratio(rewritten_text)
                threshold = 0.02  # 2% 阈值，调整为更宽松的设置

                # 计算英文字母占比
                english_chars = sum(1 for char in rewritten_text if 'a' <= char.lower() <= 'z')
                english_ratio = english_chars / total_chars if total_chars > 0 else 0
                english_threshold = 0.10  # 10% 阈值，调整为更宽松的设置

                # 检查质量 - 调整字符数阈值，使其更宽松
                min_chars = max(200, len(args[0]) * 0.3) if args else 50  # 动态设置最小字符数

                # 添加调试信息
                if non_allowed_chars:
                    logger.warning(f"检测到非法字符: {', '.join(list(non_allowed_chars)[:10])}{'...' if len(non_allowed_chars) > 10 else ''}")
                    logger.warning(f"非法字符数量: {non_allowed_count}, 总字符数: {total_chars}, 占比: {ratio:.2%}")

                if total_chars < min_chars:
                    logger.warning(f"总字符数{total_chars}小于最小要求{min_chars}，需要重试。")
                elif ratio >= threshold:
                    logger.warning(f"非法字符占比为 {ratio:.2%}，超过阈值 {threshold:.2%}，需要重试。")
                elif english_ratio >= english_threshold:
                    logger.warning(f"英文字母占比为 {english_ratio:.2%}，超过阈值 {english_threshold:.2%}，需要重试。")
                else:
                    logger.info(f"文本质量检查通过：字符数{total_chars}，非法字符占比{ratio:.2%}，英文占比{english_ratio:.2%}")
                    return result

        except BlockError as e:
            logger.warning(f"BlockError in {operation.__name__}: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Error during {operation.__name__}: {str(e)}")

        if retry < max_retries - 1:
            logger.info(f"Retrying {operation.__name__}, attempt {retry + 2}/{max_retries}")
            time.sleep(delay)
        else:
            # 最后一次重试失败，检查是否有部分可用的结果
            if result and result.get("rewritten_text"):
                logger.warning(f"最后一次重试，接受质量较低的结果：{len(result.get('rewritten_text', ''))}字符")
                return result

    logger.error(f"所有重试都失败了，{operation.__name__}返回None")
    return None


def extract_smart_ending(text):
    """智能提取文本结尾部分作为上下文"""
    if not text:
        return '无前文'

    # 按段落分割
    paragraphs = [p.strip() for p in text.split('\n\n') if p.strip()]

    if not paragraphs:
        # 如果没有段落分割，直接取最后800字符
        return text[-800:] if len(text) > 800 else text

    # 策略：取最后2-3个段落，但不超过1000字符
    result_paragraphs = []
    total_length = 0
    max_length = 1000
    max_paragraphs = 3

    for paragraph in reversed(paragraphs[-max_paragraphs:]):
        if total_length + len(paragraph) <= max_length:
            result_paragraphs.insert(0, paragraph)
            total_length += len(paragraph)
        else:
            # 如果当前段落太长，截取部分
            remaining = max_length - total_length
            if remaining > 100:  # 至少保留100字符
                truncated = paragraph[-remaining:]
                result_paragraphs.insert(0, f"...{truncated}")
            break

    return '\n\n'.join(result_paragraphs) if result_paragraphs else text[-800:]


def filter_think_tags(content: str) -> str:
    """过滤掉<think></think>标签及其内容"""
    if not content:
        return content
    return re.sub(r'<think>.*?</think>', '', content, flags=re.DOTALL)
