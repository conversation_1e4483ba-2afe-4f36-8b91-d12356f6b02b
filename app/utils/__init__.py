# Utils package for story teller application
# This package contains utility functions organized by functionality

# Import all utilities for backward compatibility
from .file_utils import *
from .text_utils import *
from .task_utils import *
from .similarity_utils import *
from .context_utils import *
from .chapter_utils import *

__all__ = [
    # File utilities
    'allowed_file',
    'save_uploaded_file',
    'get_file_size_mb',
    'split_text_into_chapters',
    'create_chapter_files',
    
    # Text utilities
    'BlockError',
    'is_allowed_character',
    'calculate_file_non_allowed_ratio',
    'retry_operation',
    'extract_smart_ending',
    'filter_think_tags',
    
    # Task utilities
    'update_task_progress',
    'merge_adapted_files',
    
    # Similarity utilities
    'find_similar_chunks',
    
    # Context utilities
    'get_smart_previous_context',
    'wait_for_previous_chapter',
    
    # Chapter utilities
    'filter_chapter_files',
    'get_chapter_files_sorted',
    'extract_chapter_number',
    'check_processed_chapters',
]
