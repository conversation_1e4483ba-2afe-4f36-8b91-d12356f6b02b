"""
Context processing utilities for chapter handling
"""
import time
import logging
from pathlib import Path
from .text_utils import extract_smart_ending

# 配置日志
logger = logging.getLogger(__name__)


def get_smart_previous_context(output_path: Path, current_chapter_num: int) -> str:
    """智能获取前文上下文 - 优化并发处理版本"""
    if current_chapter_num <= 1:
        return '无前文'

    # 直接使用原始章节文件，支持真正的并发处理
    prev_chapter_num = current_chapter_num - 1
    original_prev_name = f"chapter_{prev_chapter_num:03d}.txt"
    original_prev_path = output_path.parent / f"{output_path.parent.name.replace('_adapted', '_chapters')}" / original_prev_name

    if original_prev_path.exists():
        try:
            prev_text = original_prev_path.read_text(encoding='utf-8')
            context = extract_smart_ending(prev_text)
            logger.info(f"使用第{prev_chapter_num}章原始文本作为上下文 (长度: {len(context)}字)")
            return context
        except Exception as e:
            logger.error(f"Error reading original chapter {prev_chapter_num}: {str(e)}")

    # 备用策略: 尝试获取更前面的原始章节
    for i in range(2, min(4, current_chapter_num)):  # 最多往前找2章
        alt_chapter_num = current_chapter_num - i
        alt_original_name = f"chapter_{alt_chapter_num:03d}.txt"
        alt_original_path = output_path.parent / f"{output_path.parent.name.replace('_adapted', '_chapters')}" / alt_original_name

        if alt_original_path.exists():
            try:
                alt_text = alt_original_path.read_text(encoding='utf-8')
                context = extract_smart_ending(alt_text)
                logger.info(f"使用第{alt_chapter_num}章原始文本作为替代上下文 (长度: {len(context)}字)")
                return f"[前文摘要] {context}"
            except Exception as e:
                continue

    return '无前文'


def wait_for_previous_chapter(prev_chapter_num: int, output_path: Path, completed_chapters: set, timeout: int = 300) -> bool:
    """等待前一章完成处理"""
    # 如果前一章已经完成，直接返回
    if prev_chapter_num in completed_chapters:
        return True

    # 检查前一章的输出文件是否存在
    prev_output_name = f"chapter_{prev_chapter_num:03d}_rewrite.txt"
    prev_output_path = output_path / prev_output_name

    if prev_output_path.exists():
        completed_chapters.add(prev_chapter_num)
        return True

    # 等待前一章完成，最多等待timeout秒
    start_time = time.time()
    while time.time() - start_time < timeout:
        if prev_chapter_num in completed_chapters or prev_output_path.exists():
            completed_chapters.add(prev_chapter_num)
            return True
        time.sleep(1)  # 每秒检查一次

    logger.warning(f"等待第{prev_chapter_num}章完成超时，继续处理")
    return False
