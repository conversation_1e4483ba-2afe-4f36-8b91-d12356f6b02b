"""
File handling and upload utilities
"""
import os
import re
import uuid
from pathlib import Path
from werkzeug.utils import secure_filename
from flask import current_app


def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in current_app.config['ALLOWED_EXTENSIONS']


def save_uploaded_file(file, user_id):
    """保存上传的文件并返回文件路径"""
    if file and allowed_file(file.filename):
        # 生成安全的文件名
        filename = secure_filename(file.filename)
        # 添加UUID前缀避免文件名冲突
        unique_filename = f"{uuid.uuid4().hex}_{filename}"
        
        # 创建用户专属目录
        user_dir = Path(current_app.config['UPLOAD_FOLDER']) / str(user_id)
        user_dir.mkdir(parents=True, exist_ok=True)
        
        # 保存文件
        file_path = user_dir / unique_filename
        file.save(str(file_path))
        
        return str(file_path)
    return None


def is_inside_quotes(text, position):
    """
    检查指定位置是否在引号内部

    Args:
        text: 文本内容
        position: 要检查的位置

    Returns:
        bool: True表示在引号内部，False表示在引号外部
    """
    if position >= len(text):
        return False

    # 定义各种引号对
    quote_pairs = [
        ('"', '"'),  # 中文双引号
        ('"', '"'),  # 英文双引号
        ("'", "'"),  # 中文单引号
        ("'", "'"),  # 英文单引号
        ('「', '」'),  # 日式引号
        ('『', '』'),  # 日式双重引号
    ]

    # 检查每种引号类型
    for open_quote, close_quote in quote_pairs:
        in_quote = False

        for i in range(position):
            if i >= len(text):
                break

            char = text[i]

            if char == open_quote:
                in_quote = True
            elif char == close_quote:
                in_quote = False

        # 如果当前在这种引号内部，返回True
        if in_quote:
            return True

    return False


def is_safe_split_point(text, position):
    """
    检查指定位置是否是安全的分割点

    Args:
        text: 文本内容
        position: 要检查的位置

    Returns:
        bool: True表示是安全的分割点
    """
    if position >= len(text):
        return False

    # 检查是否在引号内部
    if is_inside_quotes(text, position):
        return False

    # 检查是否在括号内部
    bracket_pairs = [('（', '）'), ('(', ')'), ('【', '】'), ('[', ']'), ('《', '》'), ('<', '>')]

    for open_bracket, close_bracket in bracket_pairs:
        bracket_count = 0
        for i in range(position):
            if i >= len(text):
                break
            char = text[i]
            if char == open_bracket:
                bracket_count += 1
            elif char == close_bracket:
                bracket_count -= 1

        # 如果括号计数不为0，说明在括号内部
        if bracket_count > 0:
            return False

    return True


def split_by_punctuation_near_target(text_content, target_length=2000, tolerance=200):
    """
    按照目标长度附近的标点符号分割文本，避免在引号和括号内部分割

    Args:
        text_content: 要分割的文本内容
        target_length: 目标分割长度（默认2000字）
        tolerance: 容忍范围（默认200字，即在1800-2200字范围内寻找分割点）

    Returns:
        List[str]: 分割后的文本段落列表
    """
    if len(text_content) <= target_length + tolerance:
        return [text_content]

    chunks = []
    current_pos = 0
    text_len = len(text_content)

    # 定义句子结束标点符号（按优先级排序）
    end_punctuations = ['。', '！', '？', '.', '!', '?', '；', ';', '：', ':']

    while current_pos < text_len:
        # 计算理想的结束位置
        ideal_end = current_pos + target_length

        # 如果剩余文本不足目标长度+容忍范围，直接取完
        if ideal_end + tolerance >= text_len:
            chunks.append(text_content[current_pos:].strip())
            break

        # 在容忍范围内寻找最佳分割点
        best_split_pos = None
        search_start = max(current_pos + target_length - tolerance, current_pos + target_length // 2)
        search_end = min(ideal_end + tolerance, text_len)

        # 从理想位置向后搜索，优先找句号等强结束符
        for punct in end_punctuations[:3]:  # 优先考虑句号、感叹号、问号
            for i in range(ideal_end, search_end):
                if i < text_len and text_content[i] == punct:
                    # 检查是否是安全的分割点
                    next_pos = i + 1
                    if is_safe_split_point(text_content, next_pos):
                        # 检查标点符号后是否有合适的分割点（换行或空格）
                        if next_pos < text_len and text_content[next_pos] in ['\n', ' ', '\r']:
                            best_split_pos = next_pos
                            break
                        elif next_pos < text_len and text_content[next_pos:next_pos+2] in ['\n\n', '\r\n']:
                            best_split_pos = next_pos
                            break
                        else:
                            best_split_pos = next_pos
                            break
            if best_split_pos:
                break

        # 如果没找到强结束符，再找其他标点符号
        if not best_split_pos:
            for punct in end_punctuations[3:]:  # 其他标点符号
                for i in range(ideal_end, search_end):
                    if i < text_len and text_content[i] == punct:
                        next_pos = i + 1
                        if is_safe_split_point(text_content, next_pos):
                            if next_pos < text_len and text_content[next_pos] in ['\n', ' ', '\r']:
                                best_split_pos = next_pos
                                break
                            else:
                                best_split_pos = next_pos
                                break
                if best_split_pos:
                    break

        # 如果还是没找到，寻找段落分隔符（段落分隔符通常是安全的）
        if not best_split_pos:
            for i in range(search_start, search_end):
                if i < text_len - 1 and text_content[i:i+2] == '\n\n':
                    if is_safe_split_point(text_content, i + 2):
                        best_split_pos = i + 2
                        break
                elif i < text_len and text_content[i] == '\n':
                    if is_safe_split_point(text_content, i + 1):
                        best_split_pos = i + 1
                        break

        # 如果仍然没找到合适的分割点，扩大搜索范围或强制分割
        if not best_split_pos:
            # 扩大搜索范围，寻找任何安全的标点符号
            extended_search_end = min(ideal_end + tolerance * 2, text_len)
            for i in range(search_end, extended_search_end):
                if i < text_len and text_content[i] in end_punctuations:
                    next_pos = i + 1
                    if is_safe_split_point(text_content, next_pos):
                        best_split_pos = next_pos
                        break

        # 最后的回退：在理想位置强制分割（但尽量避免在引号内）
        if not best_split_pos:
            for i in range(ideal_end, min(ideal_end + tolerance, text_len)):
                if is_safe_split_point(text_content, i):
                    best_split_pos = i
                    break

            # 如果还是找不到，就强制在理想位置分割
            if not best_split_pos:
                best_split_pos = ideal_end

        # 提取当前块
        chunk = text_content[current_pos:best_split_pos].strip()
        if chunk:
            chunks.append(chunk)

        current_pos = best_split_pos

    return chunks


def split_text_into_chapters(text_content):
    """将文本内容分割成章节"""
    chapters = []

    # 更精确的章节标题模式（只匹配行首的章节标题）
    chapter_patterns = [
        r'^第[一二三四五六七八九十百千万\d]+章[^\n]*',  # 行首的第X章 + 可能的章节标题
        r'^第[0-9]+章[^\n]*',  # 行首的第数字章 + 可能的章节标题
        r'^Chapter\s*\d+[^\n]*',  # 行首的Chapter数字 + 可能的标题
        r'^[第]?[0-9]+[章节][^\n]*',  # 行首的数字章/节 + 可能的标题
        r'^[第]?[一二三四五六七八九十百千万]+[章节][^\n]*',  # 行首的中文数字章/节 + 可能的标题
    ]

    # 尝试每个模式
    for pattern in chapter_patterns:
        # 查找所有章节标题
        chapter_titles = re.findall(pattern, text_content, flags=re.IGNORECASE | re.MULTILINE)

        if len(chapter_titles) > 1:  # 找到了多个章节
            # 按章节标题分割文本
            parts = re.split(f'({pattern})', text_content, flags=re.IGNORECASE | re.MULTILINE)

            current_title = None
            current_content = []
            chapter_num = 1

            for part in parts:
                part = part.strip()
                if not part:
                    continue

                # 检查是否是章节标题
                if re.match(pattern, part, flags=re.IGNORECASE | re.MULTILINE):
                    # 保存前一章
                    if current_title and current_content:
                        content_text = '\n\n'.join(current_content).strip()
                        if content_text:  # 只有内容不为空才添加
                            chapters.append({
                                'number': chapter_num,
                                'title': current_title,
                                'content': content_text
                            })
                            chapter_num += 1

                    # 开始新章
                    current_title = part
                    current_content = []
                else:
                    # 这是章节内容
                    if part.strip():
                        current_content.append(part)

            # 添加最后一章
            if current_title and current_content:
                content_text = '\n\n'.join(current_content).strip()
                if content_text:
                    chapters.append({
                        'number': chapter_num,
                        'title': current_title,
                        'content': content_text
                    })

            # 如果找到了章节，就返回结果
            if chapters:
                break

    # 如果没有找到章节标题，使用智能分割
    if not chapters:
        # 使用2000字附近的标点符号分割
        text_chunks = split_by_punctuation_near_target(text_content, target_length=2000, tolerance=200)

        # 将分割后的文本块转换为章节格式
        for i, chunk in enumerate(text_chunks, 1):
            if chunk.strip():  # 只添加非空内容
                chapters.append({
                    'number': i,
                    'title': f'第{i}章',
                    'content': chunk.strip()
                })

    return chapters


def create_chapter_files(chapters, base_path):
    """为每个章节创建单独的文件"""
    chapter_files = []
    base_dir = Path(base_path).parent / f"{Path(base_path).stem}_chapters"
    base_dir.mkdir(parents=True, exist_ok=True)
    
    for chapter in chapters:
        chapter_filename = f"chapter_{chapter['number']:03d}.txt"
        chapter_path = base_dir / chapter_filename
        
        with open(chapter_path, 'w', encoding='utf-8') as f:
            f.write(chapter['content'])
        
        chapter_files.append(str(chapter_path))
    
    return str(base_dir), chapter_files


def get_file_size_mb(file_path):
    """获取文件大小（MB）"""
    try:
        size_bytes = os.path.getsize(file_path)
        return round(size_bytes / (1024 * 1024), 2)
    except:
        return 0
