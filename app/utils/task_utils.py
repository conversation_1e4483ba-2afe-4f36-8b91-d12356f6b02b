"""
Task progress and management utilities
"""
import re
from pathlib import Path
from datetime import datetime


def update_task_progress(celery_task, db_task, current, total, status, min_progress, max_progress):
    """更新任务进度"""
    # 计算实际进度（在min_progress和max_progress之间）
    if total > 0:
        progress_ratio = current / total
        actual_progress = min_progress + (max_progress - min_progress) * progress_ratio
    else:
        actual_progress = min_progress

    # 更新数据库
    db_task.progress = int(actual_progress)
    db_task.processed_chapters = current

    # 提交数据库事务
    from app import db
    db.session.commit()

    # 更新Celery状态
    celery_task.update_state(
        state='PROGRESS',
        meta={
            'current': int(actual_progress),
            'total': 100,
            'status': status,
            'chapters_current': current,
            'chapters_total': total
        }
    )


def merge_adapted_files(output_dir, task):
    """合并故事优化后的文件"""
    # 按章节号排序文件
    output_files = []
    for file_path in Path(output_dir).glob('*_rewrite.txt'):
        # 提取章节号进行排序
        match = re.search(r'chapter_(\d+)', file_path.name)
        if match:
            chapter_num = int(match.group(1))
            output_files.append((chapter_num, file_path))

    # 按章节号排序
    output_files.sort(key=lambda x: x[0])

    if not output_files:
        raise Exception("没有找到优化后的文件")

    # 创建最终输出文件
    final_filename = f"{task.task_name}_adapted_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    final_path = Path(output_dir).parent / final_filename

    with open(final_path, 'w', encoding='utf-8') as outfile:
        outfile.write(f"《{task.book_name}》优化版\n")
        outfile.write(f"主角：{task.character}\n")
        outfile.write(f"频道：{task.channel}\n")
        outfile.write(f"人称：第{task.person}人称\n")
        outfile.write(f"优化时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        outfile.write("=" * 50 + "\n\n")

        for chapter_num, file_path in output_files:
            with open(file_path, 'r', encoding='utf-8') as infile:
                content = infile.read().strip()
                outfile.write(f"\n\n=== 第{chapter_num}章 ===\n\n")
                outfile.write(content)
                outfile.write("\n\n")

    return str(final_path)
