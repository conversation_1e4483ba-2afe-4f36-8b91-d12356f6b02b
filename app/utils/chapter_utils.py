"""
Chapter file management and batch processing utilities
"""
import re
import logging
from pathlib import Path
from typing import List, Optional

# 配置日志
logger = logging.getLogger(__name__)





def filter_chapter_files(input_files: List[Path], start_chapter: Optional[int] = None, end_chapter: Optional[int] = None) -> List[Path]:
    """根据章节范围过滤文件"""
    if start_chapter is None and end_chapter is None:
        return input_files

    filtered_files = []
    for f in input_files:
        chapter_num = int(re.search(r'\d+', f.stem).group())
        if start_chapter is not None and chapter_num < start_chapter:
            continue
        if end_chapter is not None and chapter_num > end_chapter:
            continue
        filtered_files.append(f)

    return filtered_files


def get_chapter_files_sorted(input_path: Path, exclude_rewrite: bool = True) -> List[Path]:
    """获取排序后的章节文件列表"""
    if exclude_rewrite:
        files = [f for f in input_path.glob('*.txt') if 'rewrite' not in f.stem]
    else:
        files = list(input_path.glob('*.txt'))

    return sorted(files, key=lambda x: int(re.search(r'\d+', x.stem).group()))


def extract_chapter_number(file_path: Path) -> int:
    """从文件路径中提取章节号"""
    match = re.search(r'\d+', file_path.stem)
    if match:
        return int(match.group())
    raise ValueError(f"Cannot extract chapter number from {file_path.name}")


def check_processed_chapters(input_files: List[Path], output_path: Path) -> tuple[int, List[Path]]:
    """检查已处理的章节并返回待处理的文件列表"""
    already_processed = 0
    pending_files = []

    for file_path in input_files:
        chapter_num = extract_chapter_number(file_path)
        output_name = f"chapter_{chapter_num:03d}_rewrite.txt"
        output_file_path = output_path / output_name

        if output_file_path.exists():
            logger.info(f"Skipping already processed chapter: {chapter_num}")
            already_processed += 1
        else:
            pending_files.append(file_path)

    return already_processed, pending_files
